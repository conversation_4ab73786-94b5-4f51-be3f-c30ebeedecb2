import 'package:droit/src/widgets/AppealScreen.dart';
import 'package:droit/src/widgets/PaymentScreen.dart';
import 'package:droit/src/widgets/base_screen.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../config/colors.dart';
import '../config/models/constants.dart';
import '../config/theme_helper.dart';
import '../widgets/locale_provider.dart';

class HistoryScreen extends StatefulWidget {
  const HistoryScreen({super.key});

  @override
  _HistoryScreenState createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.locale.languageCode == 'ar';
    final colors = ThemeHelper.getColorsWithListener(context);

    return BaseScreen(
      currentIndex: 2,
      child: Column(
        children: [
          Container(
            color: colors.surface,
            child: TabBar(
              controller: _tabController,
              labelColor: AppColors.primaryOrange,
              unselectedLabelColor: colors.textSecondary,
              indicatorColor: AppColors.primaryOrange,
              labelStyle: ThemeHelper.getSectionTitleStyle(context),
              tabs: [
                Tab(text: isArabic ? 'الرخص' : 'Permis'),
                Tab(text: isArabic ? 'طلباتي' : 'Mes demandes'),
              ],
            ),
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildPermitsTab(isArabic),
                _buildRequestsTab(isArabic),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPermitsTab(bool isArabic) {
    return Padding(
      padding: Constants.screenPadding,
      child: ListView.builder(
        itemCount: 3,
        itemBuilder: (context, index) {
          return Card(
            elevation: Constants.cardTheme.elevation,
            shape: Constants.cardTheme.shape,
            color: AppColors.pureWhite,
            child: ListTile(
              leading: Icon(
                Icons.description,
                color: AppColors.info,
              ),
              title: Text(
                isArabic ? 'رخصة #${index + 1}' : 'Permis #${index + 1}',
                style: Constants.subtitleStyle,
              ),
              subtitle: Text(
                isArabic ? 'تاريخ الإصدار: 2025-04-01' : 'Date d\'émission : 01-04-2025',
                style: Constants.subtitleStyle,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildRequestsTab(bool isArabic) {
    final requests = [
      {
        'id': '1',
        'type': 'Permis de construire',
        'surface': '200',
        'status': 'approved',
        'fees': 11000.0,
        'isPaid': false,
      },
      {
        'id': '2',
        'type': 'Permis de construire',
        'surface': '150',
        'status': 'pending',
        'fees': 11000.0,
        'isPaid': false,
      },
      {
        'id': '3',
        'type': 'Permis de construire',
        'surface': '300',
        'status': 'rejected',
        'fees': 11000.0,
        'isPaid': false,
      },
    ];

    return ListView.builder(
      itemCount: requests.length,
      itemBuilder: (context, index) {
        return _buildRequestCard(context, isArabic, requests[index]);
      },
    );
  }

  Widget _buildRequestCard(BuildContext context, bool isArabic, Map<String, dynamic> request) {
    final bool isApproved = request['status'] == 'approved';
    final bool isPaid = request['isPaid'] ?? false;
    final bool isRejected = request['status'] == 'rejected';

    return Builder(
      builder: (context) {
        final colors = ThemeHelper.getColors(context);

        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          color: colors.card,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${isArabic ? 'رقم الطلب: ' : 'Demande N°: '}${request['id']}',
                      style: ThemeHelper.getSubtitleStyle(context),
                    ),
                    _buildStatusChip(isArabic, request['status']),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  '${isArabic ? 'نوع الطلب: ' : 'Type: '}${request['type']}',
                  style: ThemeHelper.getSubtitleStyle(context),
                ),
                Text(
                  '${isArabic ? 'المساحة: ' : 'Surface: '}${request['surface']} m²',
                  style: ThemeHelper.getSubtitleStyle(context),
                ),
                if (isApproved && !isPaid) ...[
                  const SizedBox(height: 16),
                  _buildPaymentButton(context, isArabic, request),
                ],
                if (isRejected) ...[
                  const SizedBox(height: 16),
                  _buildAppealButton(context, isArabic, request),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPaymentButton(BuildContext context, bool isArabic, Map<String, dynamic> request) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PaymentScreen(
                requestId: request['id'].toString(),
                amount: (request['fees'] as num?)?.toDouble() ?? 0.0,
                requestType: request['type']?.toString() ?? '',
              ),
            ),
          );
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.success,
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
        child: Text(
          isArabic ? 'دفع الرسوم' : 'Payer les frais',
          style: const TextStyle(
            color: AppColors.pureWhite,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildAppealButton(BuildContext context, bool isArabic, Map<String, dynamic> request) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AppealScreen(
                requestId: request['id'],
                requestType: request['type'],
              ),
            ),
          );
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.error,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          isArabic ? 'تقديم طعن' : 'Soumettre un recours',
          style: ThemeHelper.getButtonTextStyle(context),
        ),
      ),
    );
  }

  Widget _buildStatusChip(bool isArabic, String status) {
    Color chipColor;
    String statusText;

    switch (status) {
      case 'pending':
        chipColor = AppColors.primaryOrange;
        statusText = isArabic ? 'قيد المعالجة' : 'En cours';
        break;
      case 'approved':
        chipColor = AppColors.success;  // Changed from soothingGreen
        statusText = isArabic ? 'مقبول' : 'Approuvé';
        break;
      case 'rejected':
        chipColor = AppColors.error;    // Changed from redError
        statusText = isArabic ? 'مرفوض' : 'Rejeté';
        break;
      default:
        chipColor = AppColors.darkGray;
        statusText = isArabic ? 'غير معروف' : 'Inconnu';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: chipColor,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }
}
