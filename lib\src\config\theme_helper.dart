import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'colors.dart';
import 'theme_provider.dart';

class ThemeHelper {
  // Get colors without listener (for static usage)
  static dynamic getColors(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    return themeProvider.isDarkMode ? AppColors.dark : AppColors.light;
  }

  // Get colors with listener (for widgets that need to rebuild on theme change)
  static dynamic getColorsWithListener(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: true);
    return themeProvider.isDarkMode ? AppColors.dark : AppColors.light;
  }

  // Text Styles
  static TextStyle getTitleStyle(BuildContext context) {
    final colors = getColors(context);
    return TextStyle(
      fontSize: 28,
      fontWeight: FontWeight.bold,
      color: colors.textPrimary,
    );
  }

  static TextStyle getSectionTitleStyle(BuildContext context) {
    final colors = getColors(context);
    return TextStyle(
      fontSize: 20,
      fontWeight: FontWeight.w600,
      color: colors.textPrimary,
    );
  }

  static TextStyle getSubtitleStyle(BuildContext context) {
    final colors = getColors(context);
    return TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.normal,
      color: colors.textSecondary,
    );
  }

  static TextStyle getBodyStyle(BuildContext context) {
    final colors = getColors(context);
    return TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.normal,
      color: colors.textPrimary,
    );
  }

  static TextStyle getButtonTextStyle(BuildContext context) {
    final colors = getColors(context);
    return TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w600,
      color: colors.textOnPrimary,
    );
  }

  // Button Styles
  static ButtonStyle getPrimaryButtonStyle(BuildContext context) {
    final colors = getColors(context);
    return ElevatedButton.styleFrom(
      backgroundColor: colors.buttonPrimary,
      foregroundColor: colors.textOnPrimary,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      elevation: 2,
    );
  }

  static ButtonStyle getSecondaryButtonStyle(BuildContext context) {
    final colors = getColors(context);
    return ElevatedButton.styleFrom(
      backgroundColor: colors.buttonSecondary,
      foregroundColor: colors.textPrimary,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: colors.borderPrimary),
      ),
      elevation: 1,
    );
  }

  // Input Decoration
  static InputDecoration getInputDecoration(
    BuildContext context, {
    required String labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) {
    final colors = getColors(context);
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      labelStyle: TextStyle(color: colors.textSecondary),
      hintStyle: TextStyle(color: colors.textSecondary),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colors.borderPrimary),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colors.borderPrimary),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colors.borderFocused, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colors.borderError),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colors.borderError, width: 2),
      ),
      filled: true,
      fillColor: colors.surface,
    );
  }

  // Card Theme
  static CardTheme getCardTheme(BuildContext context) {
    final colors = getColors(context);
    return CardTheme(
      color: colors.card,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    );
  }

  // App Bar Theme
  static AppBarTheme getAppBarTheme(BuildContext context) {
    final colors = getColors(context);
    return AppBarTheme(
      backgroundColor: colors.backgroundSecondary,
      foregroundColor: colors.textPrimary,
      elevation: 0,
      titleTextStyle: getSectionTitleStyle(context),
      iconTheme: IconThemeData(color: colors.textPrimary),
    );
  }

  // Bottom Navigation Bar Theme
  static BottomNavigationBarThemeData getBottomNavTheme(BuildContext context) {
    final colors = getColors(context);
    return BottomNavigationBarThemeData(
      backgroundColor: colors.surface,
      selectedItemColor: AppColors.primaryOrange,
      unselectedItemColor: colors.textSecondary,
      type: BottomNavigationBarType.fixed,
    );
  }
}
