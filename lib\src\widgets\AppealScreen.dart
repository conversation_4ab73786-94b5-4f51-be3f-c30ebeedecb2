import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../config/colors.dart';
import '../config/theme_helper.dart';
import 'locale_provider.dart';

class AppealScreen extends StatefulWidget {
  final String requestId;
  final String requestType;

  const AppealScreen({
    Key? key,
    required this.requestId,
    required this.requestType,
  }) : super(key: key);

  @override
  State<AppealScreen> createState() => _AppealScreenState();
}

class _AppealScreenState extends State<AppealScreen> {
  final _reasonController = TextEditingController();
  final _detailsController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _reasonController.dispose();
    _detailsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.locale.languageCode == 'ar';
    final colors = ThemeHelper.getColorsWithListener(context);

    return Scaffold(
      backgroundColor: colors.backgroundSecondary,
      appBar: AppBar(
        title: Text(
          isArabic ? 'تقديم طعن' : 'Soumettre un recours',
          style: ThemeHelper.getSectionTitleStyle(context).copyWith(color: AppColors.pureWhite),
        ),
        backgroundColor: AppColors.error,
        iconTheme: const IconThemeData(color: AppColors.pureWhite),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Card(
                elevation: 2,
                color: colors.card,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isArabic ? 'معلومات الطلب' : 'Informations de la demande',
                        style: ThemeHelper.getSectionTitleStyle(context),
                      ),
                      const SizedBox(height: 16),
                      _buildInfoRow(
                        isArabic ? 'رقم الطلب:' : 'N° de demande:',
                        widget.requestId,
                        isArabic,
                      ),
                      const SizedBox(height: 8),
                      _buildInfoRow(
                        isArabic ? 'نوع الطلب:' : 'Type de demande:',
                        widget.requestType,
                        isArabic,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Card(
                elevation: 2,
                color: colors.card,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isArabic ? 'تفاصيل الطعن' : 'Détails du recours',
                        style: ThemeHelper.getSectionTitleStyle(context),
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _reasonController,
                        decoration: ThemeHelper.getInputDecoration(
                          context,
                          labelText: isArabic ? 'سبب الطعن' : 'Motif du recours',
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return isArabic
                                ? 'يرجى إدخال سبب الطعن'
                                : 'Veuillez saisir le motif du recours';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _detailsController,
                        decoration: ThemeHelper.getInputDecoration(
                          context,
                          labelText: isArabic ? 'تفاصيل إضافية' : 'Détails supplémentaires',
                        ),
                        maxLines: 5,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return isArabic
                                ? 'يرجى إدخال التفاصيل'
                                : 'Veuillez saisir les détails';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      // Implement appeal submission logic here
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            isArabic
                                ? 'تم تقديم الطعن بنجاح'
                                : 'Recours soumis avec succès',
                          ),
                          backgroundColor: AppColors.success,
                        ),
                      );
                      Navigator.pop(context);
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.error,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    isArabic ? 'تقديم الطعن' : 'Soumettre le recours',
                    style: ThemeHelper.getButtonTextStyle(context),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, bool isArabic) {
    return Builder(
      builder: (context) {
        final colors = ThemeHelper.getColors(context);

        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: ThemeHelper.getSubtitleStyle(context),
            ),
            Text(
              value,
              style: ThemeHelper.getSubtitleStyle(context).copyWith(
                fontWeight: FontWeight.bold,
                color: colors.textPrimary,
              ),
            ),
          ],
        );
      },
    );
  }
}


